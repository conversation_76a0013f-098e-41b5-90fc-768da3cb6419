<?php
/**
 * Arquivo de teste para o webhook do Tutor Webhook Automation
 * 
 * Este arquivo demonstra como enviar requisições para o webhook
 * e pode ser usado para testes durante o desenvolvimento.
 */

// Configurações do teste
$webhook_url = 'https://seusite.com/wp-json/tutor-webhook/v1/enroll';
$api_key = 'sua_chave_api_aqui'; // Deixe vazio se não configurou API key

// Dados de teste
$test_data = array(
    'email' => '<EMAIL>',
    'name' => '<PERSON>',
    'course_id' => 123 // Substitua pelo ID real de um curso
);

/**
 * Função para enviar requisição POST
 */
function send_webhook_request($url, $data, $api_key = '') {
    $headers = array(
        'Content-Type: application/json'
    );
    
    if (!empty($api_key)) {
        $headers[] = 'X-API-Key: ' . $api_key;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Para desenvolvimento local
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return array(
        'response' => $response,
        'http_code' => $http_code,
        'error' => $error
    );
}

/**
 * Executa o teste
 */
function run_test() {
    global $webhook_url, $api_key, $test_data;
    
    echo "=== TESTE DO WEBHOOK TUTOR AUTOMATION ===\n\n";
    echo "URL: {$webhook_url}\n";
    echo "Dados enviados:\n";
    echo json_encode($test_data, JSON_PRETTY_PRINT) . "\n\n";
    
    $result = send_webhook_request($webhook_url, $test_data, $api_key);
    
    echo "=== RESULTADO ===\n";
    echo "Código HTTP: {$result['http_code']}\n";
    
    if (!empty($result['error'])) {
        echo "Erro cURL: {$result['error']}\n";
    }
    
    echo "Resposta:\n";
    $response_data = json_decode($result['response'], true);
    if ($response_data) {
        echo json_encode($response_data, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo $result['response'] . "\n";
    }
    
    // Análise do resultado
    echo "\n=== ANÁLISE ===\n";
    if ($result['http_code'] == 200) {
        echo "✅ Sucesso! Usuário inscrito no curso.\n";
    } elseif ($result['http_code'] == 409) {
        echo "⚠️  Usuário já estava inscrito no curso.\n";
    } elseif ($result['http_code'] == 404) {
        echo "❌ Curso não encontrado. Verifique o course_id.\n";
    } elseif ($result['http_code'] == 401) {
        echo "❌ Não autorizado. Verifique a API key.\n";
    } elseif ($result['http_code'] == 400) {
        echo "❌ Dados inválidos. Verifique os parâmetros enviados.\n";
    } else {
        echo "❌ Erro: Código HTTP {$result['http_code']}\n";
    }
}

// Executa o teste se o arquivo for chamado diretamente
if (php_sapi_name() === 'cli') {
    run_test();
} else {
    // Se executado via browser
    echo "<pre>";
    run_test();
    echo "</pre>";
}

/**
 * Exemplos de uso com diferentes cenários
 */
function run_multiple_tests() {
    global $webhook_url, $api_key;
    
    $test_cases = array(
        array(
            'name' => 'Teste básico',
            'data' => array(
                'email' => '<EMAIL>',
                'name' => 'Usuário Teste 1',
                'course_id' => 123
            )
        ),
        array(
            'name' => 'Teste com email inválido',
            'data' => array(
                'email' => 'email-invalido',
                'name' => 'Usuário Teste 2',
                'course_id' => 123
            )
        ),
        array(
            'name' => 'Teste com course_id inválido',
            'data' => array(
                'email' => '<EMAIL>',
                'name' => 'Usuário Teste 3',
                'course_id' => 99999
            )
        ),
        array(
            'name' => 'Teste sem parâmetros obrigatórios',
            'data' => array(
                'email' => '<EMAIL>'
                // name e course_id ausentes
            )
        )
    );
    
    foreach ($test_cases as $test) {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "TESTE: {$test['name']}\n";
        echo str_repeat("=", 50) . "\n";
        
        $result = send_webhook_request($webhook_url, $test['data'], $api_key);
        
        echo "Dados: " . json_encode($test['data'], JSON_PRETTY_PRINT) . "\n";
        echo "Código HTTP: {$result['http_code']}\n";
        echo "Resposta: " . $result['response'] . "\n";
    }
}

/**
 * Exemplo de integração com Hotmart
 */
function hotmart_webhook_example() {
    // Exemplo de dados que o Hotmart envia
    $hotmart_data = array(
        'event' => 'PURCHASE_COMPLETE',
        'data' => array(
            'buyer' => array(
                'email' => '<EMAIL>',
                'name' => 'João Comprador'
            ),
            'product' => array(
                'id' => 12345,
                'name' => 'Curso de Exemplo'
            )
        )
    );
    
    // Mapeamento para o formato do nosso webhook
    $webhook_data = array(
        'email' => $hotmart_data['data']['buyer']['email'],
        'name' => $hotmart_data['data']['buyer']['name'],
        'course_id' => 123 // Mapear product_id para course_id do Tutor LMS
    );
    
    echo "=== EXEMPLO INTEGRAÇÃO HOTMART ===\n";
    echo "Dados do Hotmart:\n";
    echo json_encode($hotmart_data, JSON_PRETTY_PRINT) . "\n\n";
    echo "Dados convertidos para webhook:\n";
    echo json_encode($webhook_data, JSON_PRETTY_PRINT) . "\n";
}

/**
 * Exemplo de integração com Monetizze
 */
function monetizze_webhook_example() {
    // Exemplo de dados que o Monetizze envia
    $monetizze_data = array(
        'venda' => array(
            'status' => 'Finalizada',
            'email' => '<EMAIL>',
            'nome' => 'Maria Cliente',
            'produto' => 'Curso Online'
        )
    );
    
    // Mapeamento para o formato do nosso webhook
    $webhook_data = array(
        'email' => $monetizze_data['venda']['email'],
        'name' => $monetizze_data['venda']['nome'],
        'course_id' => 456 // Mapear produto para course_id do Tutor LMS
    );
    
    echo "=== EXEMPLO INTEGRAÇÃO MONETIZZE ===\n";
    echo "Dados do Monetizze:\n";
    echo json_encode($monetizze_data, JSON_PRETTY_PRINT) . "\n\n";
    echo "Dados convertidos para webhook:\n";
    echo json_encode($webhook_data, JSON_PRETTY_PRINT) . "\n";
}

// Descomente para executar testes adicionais
// run_multiple_tests();
// hotmart_webhook_example();
// monetizze_webhook_example();
?>
