<?php
/**
 * Plugin Name: Tu<PERSON> Webhook Automation
 * Plugin URI: https://example.com/tutor-webhook-automation
 * Description: Plugin de automação para cadastro de usuários em cursos do Tutor LMS via webhook
 * Version: 1.0.0
 * Author: Seu Nome
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: tutor-webhook-automation
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Previne acesso direto
if (!defined('ABSPATH')) {
    exit;
}

// Define constantes do plugin
define('TWA_PLUGIN_VERSION', '1.0.0');
define('TWA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TWA_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TWA_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Classe principal do plugin
 */
class TutorWebhookAutomation {
    
    /**
     * Instância única da classe
     */
    private static $instance = null;
    
    /**
     * Obtém a instância única da classe
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Construtor privado para implementar singleton
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Inicializa os hooks do WordPress
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('rest_api_init', array($this, 'register_webhook_endpoint'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Inicialização do plugin
     */
    public function init() {
        // Carrega textdomain para traduções
        load_plugin_textdomain('tutor-webhook-automation', false, dirname(TWA_PLUGIN_BASENAME) . '/languages');

        // Verifica se o Tutor LMS está ativo
        if (!$this->is_tutor_lms_active()) {
            add_action('admin_notices', array($this, 'tutor_lms_missing_notice'));
            return;
        }

        // Carrega classes necessárias
        $this->load_includes();

        // Adiciona menu de administração
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }

    /**
     * Carrega arquivos necessários
     */
    private function load_includes() {
        require_once TWA_PLUGIN_PATH . 'includes/class-webhook-handler.php';
    }
    
    /**
     * Verifica se o Tutor LMS está ativo
     */
    private function is_tutor_lms_active() {
        return class_exists('TUTOR\Tutor') || function_exists('tutor');
    }
    
    /**
     * Exibe aviso se o Tutor LMS não estiver ativo
     */
    public function tutor_lms_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('Tutor Webhook Automation requer o plugin Tutor LMS para funcionar.', 'tutor-webhook-automation'); ?></p>
        </div>
        <?php
    }
    
    /**
     * Registra o endpoint do webhook na REST API
     */
    public function register_webhook_endpoint() {
        // Endpoint genérico (mantém compatibilidade)
        register_rest_route('tutor-webhook/v1', '/enroll', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_webhook'),
            'permission_callback' => array($this, 'verify_webhook_permission'),
            'args' => array(
                'email' => array(
                    'required' => false, // Não obrigatório para permitir diferentes formatos
                    'validate_callback' => function($param) {
                        return empty($param) || is_email($param);
                    }
                ),
                'name' => array(
                    'required' => false,
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'course_id' => array(
                    'required' => false,
                    'validate_callback' => function($param) {
                        return empty($param) || (is_numeric($param) && $param > 0);
                    }
                )
            )
        ));

        // Endpoints específicos para plataformas
        register_rest_route('tutor-webhook/v1', '/hotmart', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_platform_webhook'),
            'permission_callback' => array($this, 'verify_webhook_permission')
        ));

        register_rest_route('tutor-webhook/v1', '/monetizze', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_platform_webhook'),
            'permission_callback' => array($this, 'verify_webhook_permission')
        ));

        register_rest_route('tutor-webhook/v1', '/eduzz', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_platform_webhook'),
            'permission_callback' => array($this, 'verify_webhook_permission')
        ));
    }
    
    /**
     * Verifica permissões do webhook
     */
    public function verify_webhook_permission($request) {
        // Implementar verificação de segurança (API key, token, etc.)
        $api_key = $request->get_header('X-API-Key');
        $stored_api_key = get_option('twa_api_key', '');
        
        if (empty($stored_api_key)) {
            return true; // Permitir se não houver chave configurada (para desenvolvimento)
        }
        
        return $api_key === $stored_api_key;
    }
    
    /**
     * Manipula o webhook recebido (método genérico)
     */
    public function handle_webhook($request) {
        $handler = TWA_Webhook_Handler::get_instance();
        return $handler->process_generic_webhook($request->get_params());
    }

    /**
     * Manipula webhooks de plataformas específicas
     */
    public function handle_platform_webhook($request) {
        $handler = TWA_Webhook_Handler::get_instance();
        return $handler->process_platform_webhook($request);
    }
    

    
    /**
     * Adiciona menu de administração
     */
    public function add_admin_menu() {
        add_options_page(
            'Tutor Webhook Automation',
            'Webhook Automation',
            'manage_options',
            'tutor-webhook-automation',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Página de administração
     */
    public function admin_page() {
        // Processa formulário
        if (isset($_POST['submit'])) {
            $this->process_admin_form();
            echo '<div class="notice notice-success"><p>Configurações salvas!</p></div>';
        }

        // Obtém configurações atuais
        $api_key = get_option('twa_api_key', '');
        $default_course = get_option('twa_default_course_id', '');
        $product_mappings = get_option('twa_product_mappings', array());

        // URLs dos webhooks
        $webhook_urls = array(
            'generico' => rest_url('tutor-webhook/v1/enroll'),
            'hotmart' => rest_url('tutor-webhook/v1/hotmart'),
            'monetizze' => rest_url('tutor-webhook/v1/monetizze'),
            'eduzz' => rest_url('tutor-webhook/v1/eduzz')
        );

        ?>
        <div class="wrap">
            <h1>Tutor Webhook Automation</h1>

            <div class="nav-tab-wrapper">
                <a href="#webhooks" class="nav-tab nav-tab-active">URLs dos Webhooks</a>
                <a href="#configuracoes" class="nav-tab">Configurações</a>
                <a href="#mapeamentos" class="nav-tab">Mapeamento de Produtos</a>
                <a href="#logs" class="nav-tab">Logs</a>
            </div>

            <div id="webhooks" class="tab-content">
                <div class="card">
                    <h2>URLs dos Webhooks</h2>

                    <h3>Webhook Genérico</h3>
                    <p>Para uso com dados no formato padrão:</p>
                    <code><?php echo esc_url($webhook_urls['generico']); ?></code>

                    <h3>Webhooks Específicos</h3>
                    <p><strong>Hotmart:</strong> <code><?php echo esc_url($webhook_urls['hotmart']); ?></code></p>
                    <p><strong>Monetizze:</strong> <code><?php echo esc_url($webhook_urls['monetizze']); ?></code></p>
                    <p><strong>Eduzz:</strong> <code><?php echo esc_url($webhook_urls['eduzz']); ?></code></p>

                    <h3>Parâmetros (Webhook Genérico):</h3>
                    <ul>
                        <li><strong>email</strong>: Email do usuário</li>
                        <li><strong>name</strong>: Nome completo do usuário</li>
                        <li><strong>course_id</strong>: ID do curso no Tutor LMS</li>
                    </ul>

                    <h3>Headers necessários:</h3>
                    <ul>
                        <li><strong>Content-Type</strong>: application/json</li>
                        <li><strong>X-API-Key</strong>: Sua chave de API (se configurada)</li>
                    </ul>
                </div>
            </div>

            <div id="configuracoes" class="tab-content" style="display:none;">
                <form method="post" action="">
                    <table class="form-table">
                        <tr>
                            <th scope="row">Chave de API</th>
                            <td>
                                <input type="text" name="twa_api_key" value="<?php echo esc_attr($api_key); ?>" class="regular-text" />
                                <p class="description">Deixe em branco para desabilitar verificação de API key</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Curso Padrão</th>
                            <td>
                                <?php $this->render_course_select('twa_default_course_id', $default_course); ?>
                                <p class="description">Curso usado quando não há mapeamento específico</p>
                            </td>
                        </tr>
                    </table>
                    <?php submit_button(); ?>
                </form>
            </div>

            <div id="mapeamentos" class="tab-content" style="display:none;">
                <div class="card">
                    <h2>Mapeamento de Produtos para Cursos</h2>
                    <p>Configure qual curso do Tutor LMS corresponde a cada produto das plataformas.</p>

                    <form method="post" action="">
                        <h3>Hotmart</h3>
                        <table class="widefat">
                            <thead>
                                <tr>
                                    <th>ID do Produto Hotmart</th>
                                    <th>Curso Tutor LMS</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="hotmart-mappings">
                                <?php $this->render_mapping_rows('hotmart', $product_mappings); ?>
                            </tbody>
                        </table>
                        <button type="button" onclick="addMappingRow('hotmart')" class="button">Adicionar Mapeamento</button>

                        <h3>Monetizze</h3>
                        <table class="widefat">
                            <thead>
                                <tr>
                                    <th>Nome do Produto Monetizze</th>
                                    <th>Curso Tutor LMS</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="monetizze-mappings">
                                <?php $this->render_mapping_rows('monetizze', $product_mappings); ?>
                            </tbody>
                        </table>
                        <button type="button" onclick="addMappingRow('monetizze')" class="button">Adicionar Mapeamento</button>

                        <?php submit_button('Salvar Mapeamentos'); ?>
                    </form>
                </div>
            </div>

            <div id="logs" class="tab-content" style="display:none;">
                <div class="card">
                    <h2>Logs de Webhooks</h2>
                    <?php $this->render_webhook_logs(); ?>
                </div>
            </div>
        </div>

        <script>
        // JavaScript para tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all tabs
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('nav-tab-active'));
                document.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');

                // Add active class to clicked tab
                this.classList.add('nav-tab-active');
                const target = this.getAttribute('href').substring(1);
                document.getElementById(target).style.display = 'block';
            });
        });

        // JavaScript para adicionar linhas de mapeamento
        function addMappingRow(platform) {
            const tbody = document.getElementById(platform + '-mappings');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><input type="text" name="mappings[${platform}][]" class="regular-text" /></td>
                <td><?php $this->render_course_select_js(); ?></td>
                <td><button type="button" onclick="this.parentNode.parentNode.remove()" class="button">Remover</button></td>
            `;
            tbody.appendChild(row);
        }
        </script>

        <style>
        .tab-content { margin-top: 20px; }
        .widefat th, .widefat td { padding: 10px; }
        </style>
        <?php
    }
    
    /**
     * Processa formulário da página de administração
     */
    private function process_admin_form() {
        if (isset($_POST['twa_api_key'])) {
            $api_key = sanitize_text_field($_POST['twa_api_key']);
            update_option('twa_api_key', $api_key);
        }

        if (isset($_POST['twa_default_course_id'])) {
            $default_course = intval($_POST['twa_default_course_id']);
            update_option('twa_default_course_id', $default_course);
        }

        if (isset($_POST['mappings'])) {
            $mappings = array();
            foreach ($_POST['mappings'] as $platform => $platform_mappings) {
                foreach ($platform_mappings as $index => $product_id) {
                    if (!empty($product_id) && !empty($_POST['courses'][$platform][$index])) {
                        $key = $platform . '_' . sanitize_text_field($product_id);
                        $mappings[$key] = intval($_POST['courses'][$platform][$index]);
                    }
                }
            }
            update_option('twa_product_mappings', $mappings);
        }
    }

    /**
     * Renderiza select de cursos
     */
    private function render_course_select($name, $selected = '') {
        $courses = get_posts(array(
            'post_type' => tutor()->course_post_type,
            'post_status' => 'publish',
            'numberposts' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        echo '<select name="' . esc_attr($name) . '" class="regular-text">';
        echo '<option value="">Selecione um curso</option>';
        foreach ($courses as $course) {
            $selected_attr = selected($selected, $course->ID, false);
            echo '<option value="' . esc_attr($course->ID) . '"' . $selected_attr . '>' . esc_html($course->post_title) . '</option>';
        }
        echo '</select>';
    }

    /**
     * Renderiza select de cursos para JavaScript
     */
    private function render_course_select_js() {
        $courses = get_posts(array(
            'post_type' => tutor()->course_post_type,
            'post_status' => 'publish',
            'numberposts' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        echo '<select name="courses[PLATFORM][]" class="regular-text">';
        echo '<option value="">Selecione um curso</option>';
        foreach ($courses as $course) {
            echo '<option value="' . esc_attr($course->ID) . '">' . esc_html($course->post_title) . '</option>';
        }
        echo '</select>';
    }

    /**
     * Renderiza linhas de mapeamento
     */
    private function render_mapping_rows($platform, $mappings) {
        $platform_mappings = array();
        foreach ($mappings as $key => $course_id) {
            if (strpos($key, $platform . '_') === 0) {
                $product_id = str_replace($platform . '_', '', $key);
                $platform_mappings[$product_id] = $course_id;
            }
        }

        if (empty($platform_mappings)) {
            echo '<tr>';
            echo '<td><input type="text" name="mappings[' . $platform . '][]" class="regular-text" /></td>';
            echo '<td>';
            $this->render_course_select('courses[' . $platform . '][]');
            echo '</td>';
            echo '<td><button type="button" onclick="this.parentNode.parentNode.remove()" class="button">Remover</button></td>';
            echo '</tr>';
        } else {
            foreach ($platform_mappings as $product_id => $course_id) {
                echo '<tr>';
                echo '<td><input type="text" name="mappings[' . $platform . '][]" value="' . esc_attr($product_id) . '" class="regular-text" /></td>';
                echo '<td>';
                $this->render_course_select('courses[' . $platform . '][]', $course_id);
                echo '</td>';
                echo '<td><button type="button" onclick="this.parentNode.parentNode.remove()" class="button">Remover</button></td>';
                echo '</tr>';
            }
        }
    }

    /**
     * Renderiza logs de webhooks
     */
    private function render_webhook_logs() {
        $logs = get_option('twa_webhook_logs', array());

        if (empty($logs)) {
            echo '<p>Nenhum log encontrado.</p>';
            return;
        }

        echo '<table class="widefat">';
        echo '<thead><tr><th>Data/Hora</th><th>Usuário</th><th>Curso</th><th>Status</th></tr></thead>';
        echo '<tbody>';

        $logs = array_reverse($logs); // Mais recentes primeiro
        foreach (array_slice($logs, 0, 50) as $log) { // Mostra apenas os últimos 50
            $user = get_user_by('ID', $log['user_id']);
            $course = get_post($log['course_id']);

            echo '<tr>';
            echo '<td>' . esc_html($log['timestamp']) . '</td>';
            echo '<td>' . ($user ? esc_html($user->display_name . ' (' . $user->user_email . ')') : 'Usuário não encontrado') . '</td>';
            echo '<td>' . ($course ? esc_html($course->post_title) : 'Curso não encontrado') . '</td>';
            echo '<td><span style="color: green;">✓ Sucesso</span></td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';

        if (count($logs) > 50) {
            echo '<p><em>Mostrando os últimos 50 logs de ' . count($logs) . ' total.</em></p>';
        }
    }

    /**
     * Ativação do plugin
     */
    public function activate() {
        // Flush rewrite rules para registrar endpoints
        flush_rewrite_rules();
    }

    /**
     * Desativação do plugin
     */
    public function deactivate() {
        // Flush rewrite rules para limpar endpoints
        flush_rewrite_rules();
    }
}

// Inicializa o plugin
TutorWebhookAutomation::get_instance();
