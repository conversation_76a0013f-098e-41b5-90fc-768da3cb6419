<?php
/**
 * Classe para manipular webhooks
 * 
 * @package TutorWebhookAutomation
 */

if (!defined('ABSPATH')) {
    exit;
}

class TWA_Webhook_Handler {
    
    /**
     * Instância única da classe
     */
    private static $instance = null;
    
    /**
     * Obtém a instância única da classe
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Construtor
     */
    private function __construct() {
        // Construtor privado para singleton
    }
    
    /**
     * Processa webhook de diferentes plataformas
     */
    public function process_platform_webhook($request) {
        $headers = $request->get_headers();
        $body = $request->get_body();
        $params = $request->get_params();
        
        // Detecta a plataforma baseada nos headers ou dados
        $platform = $this->detect_platform($headers, $params);
        
        switch ($platform) {
            case 'hotmart':
                return $this->process_hotmart_webhook($params);
            case 'monetizze':
                return $this->process_monetizze_webhook($params);
            case 'eduzz':
                return $this->process_eduzz_webhook($params);
            case 'generic':
            default:
                return $this->process_generic_webhook($params);
        }
    }
    
    /**
     * Detecta a plataforma baseada nos dados recebidos
     */
    private function detect_platform($headers, $params) {
        // Hotmart
        if (isset($headers['x_hotmart_hottok']) || isset($params['hottok'])) {
            return 'hotmart';
        }
        
        // Monetizze
        if (isset($params['venda']) || isset($params['postback_type'])) {
            return 'monetizze';
        }
        
        // Eduzz
        if (isset($params['transaction_status']) || isset($params['pub_id'])) {
            return 'eduzz';
        }
        
        return 'generic';
    }
    
    /**
     * Processa webhook do Hotmart
     */
    private function process_hotmart_webhook($data) {
        // Verifica se é uma compra finalizada
        if (!isset($data['event']) || $data['event'] !== 'PURCHASE_COMPLETE') {
            return new WP_Error('invalid_event', 'Evento não é uma compra finalizada', array('status' => 400));
        }
        
        // Extrai dados do comprador
        $buyer_data = $data['data']['buyer'] ?? array();
        $product_data = $data['data']['product'] ?? array();
        
        $email = $buyer_data['email'] ?? '';
        $name = $buyer_data['name'] ?? '';
        $product_id = $product_data['id'] ?? 0;
        
        // Mapeia produto para curso
        $course_id = $this->map_product_to_course($product_id, 'hotmart');
        
        if (!$course_id) {
            return new WP_Error('course_mapping_failed', 'Não foi possível mapear produto para curso', array('status' => 400));
        }
        
        return $this->enroll_user($email, $name, $course_id, $data);
    }
    
    /**
     * Processa webhook do Monetizze
     */
    private function process_monetizze_webhook($data) {
        // Verifica se é uma venda finalizada
        $venda = $data['venda'] ?? array();
        if (empty($venda) || $venda['status'] !== 'Finalizada') {
            return new WP_Error('invalid_status', 'Venda não finalizada', array('status' => 400));
        }
        
        $email = $venda['email'] ?? '';
        $name = $venda['nome'] ?? '';
        $produto = $venda['produto'] ?? '';
        
        // Mapeia produto para curso
        $course_id = $this->map_product_to_course($produto, 'monetizze');
        
        if (!$course_id) {
            return new WP_Error('course_mapping_failed', 'Não foi possível mapear produto para curso', array('status' => 400));
        }
        
        return $this->enroll_user($email, $name, $course_id, $data);
    }
    
    /**
     * Processa webhook do Eduzz
     */
    private function process_eduzz_webhook($data) {
        // Verifica se é uma transação aprovada
        if (!isset($data['transaction_status']) || $data['transaction_status'] !== 'approved') {
            return new WP_Error('invalid_status', 'Transação não aprovada', array('status' => 400));
        }
        
        $email = $data['customer_email'] ?? '';
        $name = $data['customer_name'] ?? '';
        $product_id = $data['product_id'] ?? 0;
        
        // Mapeia produto para curso
        $course_id = $this->map_product_to_course($product_id, 'eduzz');
        
        if (!$course_id) {
            return new WP_Error('course_mapping_failed', 'Não foi possível mapear produto para curso', array('status' => 400));
        }
        
        return $this->enroll_user($email, $name, $course_id, $data);
    }
    
    /**
     * Processa webhook genérico
     */
    private function process_generic_webhook($data) {
        $email = $data['email'] ?? '';
        $name = $data['name'] ?? '';
        $course_id = $data['course_id'] ?? 0;
        
        return $this->enroll_user($email, $name, $course_id, $data);
    }
    
    /**
     * Mapeia produto da plataforma para curso do Tutor LMS
     */
    private function map_product_to_course($product_identifier, $platform) {
        $mappings = get_option('twa_product_mappings', array());
        
        $key = $platform . '_' . $product_identifier;
        
        if (isset($mappings[$key])) {
            return intval($mappings[$key]);
        }
        
        // Se não houver mapeamento específico, tenta mapeamento padrão
        $default_course = get_option('twa_default_course_id', 0);
        if ($default_course) {
            return intval($default_course);
        }
        
        return false;
    }
    
    /**
     * Inscreve usuário no curso
     */
    private function enroll_user($email, $name, $course_id, $original_data = array()) {
        // Validações
        if (empty($email) || !is_email($email)) {
            return new WP_Error('invalid_email', 'Email inválido', array('status' => 400));
        }
        
        if (empty($name)) {
            return new WP_Error('invalid_name', 'Nome é obrigatório', array('status' => 400));
        }
        
        if (empty($course_id) || !is_numeric($course_id)) {
            return new WP_Error('invalid_course_id', 'ID do curso inválido', array('status' => 400));
        }
        
        // Verifica se o curso existe
        if (!$this->course_exists($course_id)) {
            return new WP_Error('course_not_found', 'Curso não encontrado', array('status' => 404));
        }
        
        // Cria ou obtém usuário
        $user_id = $this->create_or_get_user($email, $name);
        if (is_wp_error($user_id)) {
            return $user_id;
        }
        
        // Inscreve no curso
        $enrollment_result = $this->enroll_user_in_course($user_id, $course_id);
        if (is_wp_error($enrollment_result)) {
            return $enrollment_result;
        }
        
        // Salva dados originais do webhook para referência
        $this->save_webhook_data($user_id, $course_id, $original_data);
        
        // Hook para ações personalizadas
        do_action('twa_after_enrollment', $user_id, $course_id, $enrollment_result, $original_data);
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Usuário inscrito com sucesso no curso',
            'data' => array(
                'user_id' => $user_id,
                'course_id' => $course_id,
                'enrollment_id' => $enrollment_result
            )
        ), 200);
    }
    
    /**
     * Verifica se o curso existe
     */
    private function course_exists($course_id) {
        $course = get_post($course_id);
        return $course && $course->post_type === tutor()->course_post_type && $course->post_status === 'publish';
    }
    
    /**
     * Cria ou obtém usuário existente
     */
    private function create_or_get_user($email, $name) {
        // Verifica se usuário já existe
        $existing_user = get_user_by('email', $email);
        if ($existing_user) {
            return $existing_user->ID;
        }
        
        // Cria novo usuário
        $username = $this->generate_username($email);
        $password = wp_generate_password(12, false);
        
        $user_data = array(
            'user_login' => $username,
            'user_email' => $email,
            'user_pass' => $password,
            'display_name' => $name,
            'first_name' => $this->extract_first_name($name),
            'last_name' => $this->extract_last_name($name),
            'role' => 'subscriber'
        );
        
        // Permite personalização dos dados do usuário
        $user_data = apply_filters('twa_user_data', $user_data, $email, $name);
        
        $user_id = wp_insert_user($user_data);
        
        if (is_wp_error($user_id)) {
            return new WP_Error('user_creation_failed', 'Falha ao criar usuário: ' . $user_id->get_error_message(), array('status' => 500));
        }
        
        // Hook após criação do usuário
        do_action('twa_after_user_creation', $user_id, $user_data);
        
        // Envia email de boas-vindas
        $this->send_welcome_email($user_id, $password);
        
        return $user_id;
    }
    
    /**
     * Gera nome de usuário único
     */
    private function generate_username($email) {
        $username = sanitize_user(substr($email, 0, strpos($email, '@')));
        
        $original_username = $username;
        $counter = 1;
        while (username_exists($username)) {
            $username = $original_username . $counter;
            $counter++;
        }
        
        return $username;
    }
    
    /**
     * Extrai primeiro nome
     */
    private function extract_first_name($full_name) {
        $names = explode(' ', trim($full_name));
        return $names[0];
    }
    
    /**
     * Extrai último nome
     */
    private function extract_last_name($full_name) {
        $names = explode(' ', trim($full_name));
        if (count($names) > 1) {
            return end($names);
        }
        return '';
    }
    
    /**
     * Inscreve usuário no curso
     */
    private function enroll_user_in_course($user_id, $course_id) {
        // Verifica se já está inscrito
        if (tutor_utils()->is_enrolled($course_id, $user_id)) {
            return new WP_Error('already_enrolled', 'Usuário já está inscrito neste curso', array('status' => 409));
        }
        
        // Realiza inscrição
        $enrollment_id = tutor_utils()->do_enroll($course_id, 0, $user_id);
        
        if (!$enrollment_id) {
            return new WP_Error('enrollment_failed', 'Falha ao inscrever usuário no curso', array('status' => 500));
        }
        
        return $enrollment_id;
    }
    
    /**
     * Envia email de boas-vindas
     */
    private function send_welcome_email($user_id, $password) {
        $user = get_user_by('ID', $user_id);
        if (!$user) return;
        
        $subject = apply_filters('twa_welcome_email_subject', 'Bem-vindo! Sua conta foi criada', $user);
        
        $message = sprintf(
            "Olá %s,\n\nSua conta foi criada com sucesso!\n\nDados de acesso:\nEmail: %s\nSenha: %s\n\nFaça login em: %s\n\nObrigado!",
            $user->display_name,
            $user->user_email,
            $password,
            wp_login_url()
        );
        
        $message = apply_filters('twa_welcome_email_message', $message, $user, $password);
        
        wp_mail($user->user_email, $subject, $message);
    }
    
    /**
     * Salva dados do webhook para referência
     */
    private function save_webhook_data($user_id, $course_id, $data) {
        $webhook_data = array(
            'timestamp' => current_time('mysql'),
            'user_id' => $user_id,
            'course_id' => $course_id,
            'original_data' => $data
        );
        
        // Salva como meta do usuário
        add_user_meta($user_id, '_twa_webhook_data', $webhook_data);
        
        // Salva log global
        $logs = get_option('twa_webhook_logs', array());
        $logs[] = $webhook_data;
        
        // Mantém apenas os últimos 100 logs
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }
        
        update_option('twa_webhook_logs', $logs);
    }
}
