# Guia de Instalação - Tutor Webhook Automation

## Pré-requisitos

Antes de instalar o plugin, certifique-se de que você tem:

- ✅ WordPress 5.0 ou superior
- ✅ PHP 7.4 ou superior
- ✅ Plugin Tutor LMS instalado e ativo
- ✅ Pelo menos um curso criado no Tutor LMS
- ✅ Acesso ao painel administrativo do WordPress

## Instalação

### Método 1: Upload via WordPress Admin

1. Faça download do plugin (pasta `tutor-webhook-automation`)
2. Compacte a pasta em um arquivo ZIP
3. No WordPress Admin, vá em **Plugins > Adicionar Novo**
4. Clique em **Enviar Plugin**
5. Selecione o arquivo ZIP e clique em **Instalar Agora**
6. Após a instalação, clique em **Ativar Plugin**

### Método 2: Upload via FTP

1. Faça upload da pasta `tutor-webhook-automation` para `/wp-content/plugins/`
2. No WordPress Admin, vá em **Plugins**
3. Encontre "Tutor Webhook Automation" e clique em **Ativar**

### Método 3: Instalação Manual

1. Copie a pasta `tutor-webhook-automation` para `/wp-content/plugins/`
2. Certifique-se de que as permissões estão corretas (755 para pastas, 644 para arquivos)
3. Ative o plugin no painel administrativo

## Configuração Inicial

### 1. Verificar Instalação

Após ativar o plugin, você deve ver:
- Menu "Webhook Automation" em **Configurações**
- Nenhum erro na tela de plugins
- Mensagem de sucesso na ativação

### 2. Configurar API Key (Recomendado)

1. Vá em **Configurações > Webhook Automation**
2. Na aba "Configurações", defina uma **Chave de API** forte
3. Exemplo de chave forte: `wh_live_sk_1234567890abcdef`
4. Clique em **Salvar Alterações**

### 3. Configurar Curso Padrão (Opcional)

1. Na mesma página, selecione um **Curso Padrão**
2. Este curso será usado quando não houver mapeamento específico
3. Clique em **Salvar Alterações**

### 4. Configurar Mapeamentos de Produtos

1. Vá na aba "Mapeamento de Produtos"
2. Configure qual curso corresponde a cada produto das plataformas:

**Para Hotmart:**
- ID do Produto Hotmart: `12345`
- Curso Tutor LMS: Selecione o curso desejado

**Para Monetizze:**
- Nome do Produto: `Curso de Marketing Digital`
- Curso Tutor LMS: Selecione o curso desejado

3. Clique em **Salvar Mapeamentos**

## URLs dos Webhooks

Após a configuração, você terá acesso às seguintes URLs:

### Webhook Genérico
```
https://seusite.com/wp-json/tutor-webhook/v1/enroll
```

### Webhooks Específicos
```
https://seusite.com/wp-json/tutor-webhook/v1/hotmart
https://seusite.com/wp-json/tutor-webhook/v1/monetizze
https://seusite.com/wp-json/tutor-webhook/v1/eduzz
```

## Configuração nas Plataformas

### Hotmart

1. Acesse o painel do Hotmart
2. Vá em **Configurações > Webhooks**
3. Adicione nova URL: `https://seusite.com/wp-json/tutor-webhook/v1/hotmart`
4. Selecione eventos: **PURCHASE_COMPLETE**
5. Adicione header: `X-API-Key: sua_chave_api`

### Monetizze

1. Acesse o painel do Monetizze
2. Vá em **Configurações > Postback**
3. URL do Postback: `https://seusite.com/wp-json/tutor-webhook/v1/monetizze`
4. Método: **POST**
5. Formato: **JSON**

### Eduzz

1. Acesse o painel do Eduzz
2. Vá em **Configurações > Postback**
3. URL: `https://seusite.com/wp-json/tutor-webhook/v1/eduzz`
4. Eventos: **Transação Aprovada**

## Teste da Instalação

### 1. Teste Manual

Use o arquivo `test-webhook.php` incluído no plugin:

```bash
php test-webhook.php
```

### 2. Teste via cURL

```bash
curl -X POST https://seusite.com/wp-json/tutor-webhook/v1/enroll \
  -H "Content-Type: application/json" \
  -H "X-API-Key: sua_chave_api" \
  -d '{
    "email": "<EMAIL>",
    "name": "João Teste",
    "course_id": 123
  }'
```

### 3. Teste via Postman

1. Método: **POST**
2. URL: `https://seusite.com/wp-json/tutor-webhook/v1/enroll`
3. Headers:
   - `Content-Type: application/json`
   - `X-API-Key: sua_chave_api`
4. Body (JSON):
```json
{
  "email": "<EMAIL>",
  "name": "João Teste",
  "course_id": 123
}
```

## Verificação de Logs

### 1. Logs do Plugin

1. Vá em **Configurações > Webhook Automation**
2. Clique na aba **Logs**
3. Verifique se as requisições estão sendo registradas

### 2. Logs do WordPress

Se `WP_DEBUG` estiver ativo, verifique `/wp-content/debug.log`

### 3. Logs do Servidor

Verifique os logs de erro do seu servidor web (Apache/Nginx)

## Solução de Problemas

### Erro: "Tutor LMS não encontrado"

**Solução:** Instale e ative o plugin Tutor LMS

### Erro: "Curso não encontrado"

**Soluções:**
1. Verifique se o `course_id` está correto
2. Certifique-se de que o curso está publicado
3. Verifique se é um post do tipo `tutor_course`

### Erro: "Não autorizado"

**Soluções:**
1. Verifique se a API key está correta
2. Certifique-se de enviar o header `X-API-Key`
3. Verifique se não há espaços extras na chave

### Erro: "Email inválido"

**Soluções:**
1. Verifique o formato do email
2. Certifique-se de que o campo `email` está sendo enviado
3. Verifique se não há caracteres especiais

### Webhook não está funcionando

**Soluções:**
1. Verifique se as URLs estão corretas
2. Teste com cURL ou Postman primeiro
3. Verifique os logs de erro
4. Certifique-se de que o SSL está funcionando
5. Verifique se não há firewall bloqueando

### Usuário criado mas não inscrito no curso

**Soluções:**
1. Verifique se o Tutor LMS está funcionando corretamente
2. Teste inscrição manual no curso
3. Verifique se há conflitos com outros plugins
4. Verifique os logs para erros específicos

## Configurações Avançadas

### Personalizar Emails

Adicione ao `functions.php` do seu tema:

```php
function personalizar_email_webhook($message, $user, $password) {
    return "Seu email personalizado aqui...";
}
add_filter('twa_welcome_email_message', 'personalizar_email_webhook', 10, 3);
```

### Ações Personalizadas

```php
function acao_apos_inscricao($user_id, $course_id, $enrollment_id) {
    // Sua lógica personalizada
}
add_action('twa_after_enrollment', 'acao_apos_inscricao', 10, 3);
```

### Modificar Dados do Usuário

```php
function modificar_dados_usuario($user_data, $email, $name) {
    $user_data['role'] = 'custom_role';
    return $user_data;
}
add_filter('twa_user_data', 'modificar_dados_usuario', 10, 3);
```

## Segurança

### Recomendações

1. **Sempre use HTTPS** para webhooks
2. **Configure API key forte** com pelo menos 32 caracteres
3. **Monitore logs regularmente** para detectar tentativas de abuso
4. **Mantenha o WordPress atualizado**
5. **Use firewall** para proteger endpoints

### Exemplo de API Key Forte

```
wh_live_sk_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t
```

## Suporte

Para suporte técnico:

1. Verifique este guia de instalação
2. Consulte o arquivo `README.md`
3. Verifique os logs de erro
4. Teste com dados simples primeiro
5. Verifique se todos os pré-requisitos estão atendidos

## Próximos Passos

Após a instalação bem-sucedida:

1. Configure webhooks nas suas plataformas de pagamento
2. Teste com transações reais (pequenos valores)
3. Configure emails personalizados se necessário
4. Monitore logs regularmente
5. Configure backup dos dados de webhook

## Desinstalação

Para remover completamente o plugin:

1. Desative o plugin
2. Exclua o plugin
3. Remova configurações (opcional):
   - `twa_api_key`
   - `twa_default_course_id`
   - `twa_product_mappings`
   - `twa_webhook_logs`
