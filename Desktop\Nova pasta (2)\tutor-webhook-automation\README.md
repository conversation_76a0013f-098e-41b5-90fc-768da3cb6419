# Tutor Webhook Automation

Plugin WordPress para automação de cadastro de usuários em cursos do Tutor LMS via webhook.

## Descrição

Este plugin permite automatizar o fluxo de inscrição de usuários em cursos do Tutor LMS através de webhooks. Quando uma requisição POST é enviada para o endpoint do plugin, ele:

1. Cria automaticamente um usuário WordPress (se não existir)
2. Inscreve o usuário no curso específico do Tutor LMS
3. Envia email de boas-vindas com dados de acesso

## Requisitos

- WordPress 5.0 ou superior
- PHP 7.4 ou superior
- Plugin Tutor LMS ativo

## Instalação

1. Faça upload da pasta `tutor-webhook-automation` para o diretório `/wp-content/plugins/`
2. Ative o plugin através do menu 'Plugins' no WordPress
3. Configure as opções em 'Configurações' > 'Webhook Automation'

## Configuração

### 1. Chave de API (Opcional)

Para maior segurança, configure uma chave de API em:
- WordPress Admin > Configurações > Webhook Automation
- Defina uma chave de API forte
- Esta chave deve ser enviada no header `X-API-Key` de todas as requisições

### 2. URL do Webhook

O endpoint do webhook estará disponível em:
```
https://seusite.com/wp-json/tutor-webhook/v1/enroll
```

## Uso

### Requisição HTTP

**Método:** POST  
**URL:** `https://seusite.com/wp-json/tutor-webhook/v1/enroll`  
**Content-Type:** `application/json`

### Headers Necessários

```
Content-Type: application/json
X-API-Key: sua_chave_api_aqui (se configurada)
```

### Parâmetros Obrigatórios

```json
{
    "email": "<EMAIL>",
    "name": "Nome Completo do Usuário",
    "course_id": 123
}
```

### Exemplo de Requisição

```bash
curl -X POST https://seusite.com/wp-json/tutor-webhook/v1/enroll \
  -H "Content-Type: application/json" \
  -H "X-API-Key: sua_chave_api" \
  -d '{
    "email": "<EMAIL>",
    "name": "João Silva",
    "course_id": 456
  }'
```

### Exemplo de Resposta de Sucesso

```json
{
    "success": true,
    "message": "Usuário inscrito com sucesso no curso",
    "data": {
        "user_id": 789,
        "course_id": 456,
        "enrollment_id": 101112
    }
}
```

### Exemplo de Resposta de Erro

```json
{
    "code": "course_not_found",
    "message": "Curso não encontrado",
    "data": {
        "status": 404
    }
}
```

## Funcionalidades

### ✅ Criação Automática de Usuários
- Verifica se o usuário já existe pelo email
- Cria novo usuário se não existir
- Gera username único baseado no email
- Gera senha segura automaticamente
- Define role como 'subscriber'

### ✅ Inscrição em Cursos
- Verifica se o curso existe e está publicado
- Verifica se o usuário já está inscrito
- Utiliza a função nativa `tutor_utils()->do_enroll()` do Tutor LMS
- Suporta cursos gratuitos e pagos

### ✅ Email de Boas-vindas
- Envia automaticamente email com dados de acesso
- Inclui link para login
- Personalizável via filtros do WordPress

### ✅ Segurança
- Validação de dados de entrada
- Sanitização de parâmetros
- Verificação de API key opcional
- Logs de debug (quando WP_DEBUG ativo)

### ✅ Tratamento de Erros
- Respostas padronizadas da REST API
- Códigos de erro específicos
- Mensagens descritivas

## Códigos de Erro

| Código | Descrição |
|--------|-----------|
| `course_not_found` | Curso não encontrado ou não publicado |
| `user_creation_failed` | Falha ao criar usuário WordPress |
| `already_enrolled` | Usuário já inscrito no curso |
| `enrollment_failed` | Falha ao inscrever usuário no curso |
| `webhook_error` | Erro geral no processamento |

## Logs e Debug

Para ativar logs de debug, adicione ao `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Os logs serão salvos em `/wp-content/debug.log`

## Personalização

### Filtros Disponíveis

```php
// Personalizar dados do usuário antes da criação
add_filter('twa_user_data', function($user_data, $email, $name) {
    // Modificar $user_data
    return $user_data;
}, 10, 3);

// Personalizar email de boas-vindas
add_filter('twa_welcome_email_subject', function($subject, $user) {
    return "Bem-vindo ao nosso site, {$user->display_name}!";
}, 10, 2);

add_filter('twa_welcome_email_message', function($message, $user, $password) {
    // Personalizar mensagem
    return $message;
}, 10, 3);
```

### Actions Disponíveis

```php
// Executar ação após inscrição bem-sucedida
add_action('twa_after_enrollment', function($user_id, $course_id, $enrollment_id) {
    // Sua lógica personalizada
}, 10, 3);

// Executar ação após criação de usuário
add_action('twa_after_user_creation', function($user_id, $user_data) {
    // Sua lógica personalizada
}, 10, 2);
```

## Integração com Plataformas de Pagamento

Este plugin pode ser integrado com diversas plataformas:

- **Hotmart**: Configure webhook no painel do Hotmart
- **Monetizze**: Use webhook de conversão
- **Eduzz**: Configure postback de venda
- **PagSeguro**: Use notificações de transação
- **PayPal**: Configure IPN ou webhooks
- **Stripe**: Use webhooks de checkout

## Suporte

Para suporte e dúvidas:
1. Verifique os logs de erro
2. Teste com ferramentas como Postman ou cURL
3. Verifique se o Tutor LMS está ativo
4. Confirme se o curso_id existe e está publicado

## Changelog

### 1.0.0
- Versão inicial
- Criação automática de usuários
- Inscrição em cursos do Tutor LMS
- Email de boas-vindas
- Verificação de API key
- Interface de administração

## Licença

GPL v2 ou posterior

## Autor

Desenvolvido para automação de vendas com Tutor LMS.
